package microservices.authservice.authservice.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class RefreshTokenService {

    private final RedisTemplate<String, Object> redisTemplate;
    private static final String REFRESH_TOKEN_PREFIX = "refresh_token:";
    private static final Duration REFRESH_TOKEN_EXPIRY = Duration.ofDays(7);

    public String createRefreshToken(Long userId) {
        String refreshToken = UUID.randomUUID().toString();
        String key = REFRESH_TOKEN_PREFIX + refreshToken;
        
        redisTemplate.opsForValue().set(key, userId, REFRESH_TOKEN_EXPIRY);
        log.info("Created refresh token for user: {}", userId);
        
        return refreshToken;
    }

    public Long getUserIdFromRefreshToken(String refreshToken) {
        String key = REFRESH_TOKEN_PREFIX + refreshToken;
        Object userId = redisTemplate.opsForValue().get(key);
        
        if (userId != null) {
            return Long.valueOf(userId.toString());
        }
        
        log.warn("Refresh token not found or expired: {}", refreshToken);
        return null;
    }

    public boolean validateRefreshToken(String refreshToken) {
        String key = REFRESH_TOKEN_PREFIX + refreshToken;
        return redisTemplate.hasKey(key);
    }

    public void deleteRefreshToken(String refreshToken) {
        String key = REFRESH_TOKEN_PREFIX + refreshToken;
        redisTemplate.delete(key);
        log.info("Deleted refresh token: {}", refreshToken);
    }

    public void deleteAllRefreshTokensForUser(Long userId) {
        // This is a simple implementation - in production you might want to maintain a user->tokens mapping
        log.info("Request to delete all refresh tokens for user: {}", userId);
    }
}
