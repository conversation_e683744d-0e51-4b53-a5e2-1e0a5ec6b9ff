package microservices.productservice.productservice.readmodel;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.index.Indexed;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Document(collection = "product_summaries")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductSummaryReadModel {
    
    @Id
    private String id;
    
    @Indexed
    private Long productId;
    
    @Indexed
    private String name;
    
    private BigDecimal price;
    
    @Indexed
    private Integer quantity;
    
    @Indexed
    private Long categoryId;
    
    private String categoryName;
    
    @Indexed
    private Long brandId;
    
    private String brandName;
    
    @Indexed
    private String status;
    
    private String imageUrl;
    
    private Double averageRating;
    
    private Integer reviewCount;
    
    @Indexed
    private Boolean isAvailable;
    
    @Indexed
    private Boolean isFeatured;
    
    @Indexed
    private LocalDateTime createdAt;
    
    @Indexed
    private LocalDateTime updatedAt;
}
