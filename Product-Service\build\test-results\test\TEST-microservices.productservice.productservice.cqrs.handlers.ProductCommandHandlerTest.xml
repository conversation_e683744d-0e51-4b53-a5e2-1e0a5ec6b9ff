<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="microservices.productservice.productservice.cqrs.handlers.ProductCommandHandlerTest" tests="1" skipped="0" failures="0" errors="0" timestamp="2025-06-03T07:08:12" hostname="LHN-820-180225" time="0.865">
  <properties/>
  <testcase name="shouldHandleCreateProductCommandSuccessfully()" classname="microservices.productservice.productservice.cqrs.handlers.ProductCommandHandlerTest" time="0.865"/>
  <system-out><![CDATA[14:08:13.245 [Test worker] INFO microservices.productservice.productservice.cqrs.handlers.ProductCommandHandler - Handling CreateProductCommand for product: Test Product
14:08:13.261 [Test worker] INFO microservices.productservice.productservice.cqrs.handlers.ProductCommandHandler - Product created successfully with ID: 1
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
