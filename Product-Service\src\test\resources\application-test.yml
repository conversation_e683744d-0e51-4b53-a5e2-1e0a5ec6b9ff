spring:
  application:
    name: PRODUCT-SERVICE-TEST

  # Test Database Configuration
  datasource:
    url: jdbc:h2:mem:testdb
    username: sa
    password: 
    driver-class-name: org.h2.Driver

  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect

  # Test MongoDB Configuration (will be overridden by TestContainers)
  data:
    mongodb:
      host: localhost
      port: 27017
      database: test_product_read_db

  # Test Redis Configuration (will be overridden by TestContainers)
  data:
    redis:
      host: localhost
      port: 6379

  # Test Kafka Configuration (will be overridden by TestContainers)
  kafka:
    bootstrap-servers: localhost:9092
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
    consumer:
      group-id: product-service-test-group
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      auto-offset-reset: earliest

  # Disable cloud config for tests
  cloud:
    config:
      enabled: false
    discovery:
      enabled: false

# Test server configuration
server:
  port: 0

# Disable Eureka for tests
eureka:
  client:
    enabled: false

# Logging for tests
logging:
  level:
    microservices.productservice: DEBUG
    org.springframework.kafka: WARN
    org.springframework.data.mongodb: WARN
