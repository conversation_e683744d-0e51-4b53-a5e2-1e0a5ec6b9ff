package microservices.productservice.productservice.cqrs.handlers;

import microservices.productservice.productservice.cqrs.commands.CreateProductCommand;
import microservices.productservice.productservice.cqrs.commands.ReduceProductQuantityCommand;
import microservices.productservice.productservice.domain.entity.Product;
import microservices.productservice.productservice.repository.write.ProductWriteRepository;
import microservices.productservice.productservice.service.EventPublisher;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ProductCommandHandlerTest {

    @Mock
    private ProductWriteRepository productWriteRepository;

    @Mock
    private EventPublisher eventPublisher;

    private ProductCommandHandler productCommandHandler;

    @BeforeEach
    void setUp() {
        productCommandHandler = new ProductCommandHandler(productWriteRepository, eventPublisher);
    }

    @Test
    void shouldHandleCreateProductCommandSuccessfully() {
        // Given
        Map<String, Object> attributeValues = new HashMap<>();
        attributeValues.put("color", "Red");
        attributeValues.put("size", "Large");

        CreateProductCommand command = CreateProductCommand.builder()
                .name("Test Product")
                .price(new BigDecimal("99.99"))
                .quantity(10)
                .description("Test description")
                .categoryId(1L)
                .brandId(1L)
                .attributeSetId(1L)
                .attributeValues(attributeValues)
                .status("ACTIVE")
                .build();

        Product savedProduct = Product.builder()
                .id(1L)
                .name("Test Product")
                .price(new BigDecimal("99.99"))
                .quantity(10)
                .description("Test description")
                .categoryId(1L)
                .brandId(1L)
                .attributeSetId(1L)
                .status(Product.ProductStatus.ACTIVE)
                .version(1L)
                .build();

        when(productWriteRepository.save(any(Product.class))).thenReturn(savedProduct);

        // When
        Long productId = productCommandHandler.handle(command);

        // Then
        assertThat(productId).isEqualTo(1L);
        verify(productWriteRepository, times(2)).save(any(Product.class)); // Once for product, once for attributes
        verify(eventPublisher).publishEvent(any());
    }

    @Test
    void shouldHandleReduceQuantityCommandSuccessfully() {
        // Given
        ReduceProductQuantityCommand command = ReduceProductQuantityCommand.builder()
                .productId(1L)
                .quantity(3)
                .reason("Sale")
                .version(1L)
                .build();

        Product product = Product.builder()
                .id(1L)
                .quantity(7) // After reduction
                .version(2L)
                .build();

        when(productWriteRepository.reduceQuantity(1L, 3, 1L)).thenReturn(1);
        when(productWriteRepository.findById(1L)).thenReturn(Optional.of(product));

        // When
        productCommandHandler.handle(command);

        // Then
        verify(productWriteRepository).reduceQuantity(1L, 3, 1L);
        verify(eventPublisher).publishEvent(any());
    }

    @Test
    void shouldThrowExceptionWhenReduceQuantityFails() {
        // Given
        ReduceProductQuantityCommand command = ReduceProductQuantityCommand.builder()
                .productId(1L)
                .quantity(10)
                .reason("Sale")
                .version(1L)
                .build();

        when(productWriteRepository.reduceQuantity(1L, 10, 1L)).thenReturn(0); // No rows updated

        // When & Then
        assertThatThrownBy(() -> productCommandHandler.handle(command))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("Failed to reduce product quantity");

        verify(eventPublisher, never()).publishEvent(any());
    }
}
