server:
  port: 8084

spring:
  application:
    name: auth-service
  cloud:
    config:
      enabled: false
      fail-fast: false

  datasource:
    url: *******************************************************************************************************************
    username: root
    password: mk123654
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: true

  data:
    redis:
      host: localhost
      port: 6379
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
  
  security:
    oauth2:
      client:
        registration:
          github:
            client-id: ${GITHUB_CLIENT_ID:Ov23l1PpstZ09LdPgcvV}
            client-secret: ${GITHUB_CLIENT_SECRET:your-github-client-secret}
            scope:
              - user:email
              - read:user
            redirect-uri: "{baseUrl}/oauth2/callback/{registrationId}"
        provider:
          github:
            authorization-uri: https://github.com/login/oauth/authorize
            token-uri: https://github.com/login/oauth/access_token
            user-info-uri: https://api.github.com/user
            user-name-attribute: id

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    fetch-registry: true
    register-with-eureka: true
  instance:
    hostname: localhost
    prefer-ip-address: true

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
  tracing:
    enabled: false
  zipkin:
    tracing:
      endpoint: ""
  observations:
    web:
      requests:
        enabled: false

jwt:
  secret: ${JWT_SECRET:myVerySecretKeyForJWTTokenGenerationThatShouldBeAtLeast256BitsLong}
  expiration: 86400000 # 24 hours in milliseconds
  refresh:
    expiration: 604800000 # 7 days in milliseconds

app:
  oauth2:
    authorized-redirect-uri: ${FRONTEND_URL:http://localhost:3000}/oauth2/redirect

logging:
  level:
    microservices.authservice: DEBUG
    org.springframework.security: DEBUG
