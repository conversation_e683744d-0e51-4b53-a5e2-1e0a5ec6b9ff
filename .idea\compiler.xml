<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Gradle Imported" enabled="true">
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.projectlombok/lombok/1.18.26/8f8cf0372abf564913e9796623aac4c8ea44025a/lombok-1.18.26.jar" />
        </processorPath>
        <module name="Product-Service.main" />
      </profile>
    </annotationProcessing>
  </component>
</project>