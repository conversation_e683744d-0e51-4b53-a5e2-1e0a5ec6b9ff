package microservices.productservice.productservice.readmodel;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Document(collection = "products")
@CompoundIndexes({
    @CompoundIndex(name = "category_status_idx", def = "{'categoryId': 1, 'status': 1}"),
    @CompoundIndex(name = "brand_status_idx", def = "{'brandId': 1, 'status': 1}"),
    @CompoundIndex(name = "price_range_idx", def = "{'price': 1, 'status': 1}"),
    @CompoundIndex(name = "search_idx", def = "{'name': 'text', 'description': 'text'}")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductReadModel {
    
    @Id
    private String id;
    
    @Indexed
    private Long productId;
    
    @Indexed
    private String name;
    
    private BigDecimal price;
    
    @Indexed
    private Integer quantity;
    
    private String description;
    
    @Indexed
    private Long categoryId;
    
    private String categoryName;
    
    @Indexed
    private Long brandId;
    
    private String brandName;
    
    @Indexed
    private String status;
    
    private Long attributeSetId;
    
    private String attributeSetName;
    
    // Denormalized attribute values for fast queries
    private Map<String, Object> attributes;
    
    // Pre-computed fields for performance
    private List<String> tags;
    
    private Double averageRating;
    
    private Integer reviewCount;
    
    private Boolean isAvailable;
    
    private Boolean isFeatured;
    
    private Boolean isOnSale;
    
    private BigDecimal originalPrice;
    
    private BigDecimal discountPercentage;
    
    // Search optimization
    private List<String> searchKeywords;
    
    // Timestamps
    @Indexed
    private LocalDateTime createdAt;
    
    @Indexed
    private LocalDateTime updatedAt;
    
    private LocalDateTime lastSyncAt;
    
    // Version for optimistic locking
    private Long version;
    
    // Computed methods
    public boolean isInStock() {
        return quantity != null && quantity > 0;
    }
    
    public boolean isActive() {
        return "ACTIVE".equals(status);
    }
    
    public BigDecimal getEffectivePrice() {
        if (isOnSale != null && isOnSale && originalPrice != null) {
            return originalPrice;
        }
        return price;
    }
}
