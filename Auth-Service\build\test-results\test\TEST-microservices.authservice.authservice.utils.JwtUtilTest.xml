<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="microservices.authservice.authservice.utils.JwtUtilTest" tests="4" skipped="0" failures="0" errors="0" timestamp="2025-05-29T09:26:48" hostname="LHN-820-180225" time="0.11">
  <properties/>
  <testcase name="shouldGenerateValidToken()" classname="microservices.authservice.authservice.utils.JwtUtilTest" time="0.067"/>
  <testcase name="shouldGenerateRefreshToken()" classname="microservices.authservice.authservice.utils.JwtUtilTest" time="0.005"/>
  <testcase name="shouldDistinguishRefreshToken()" classname="microservices.authservice.authservice.utils.JwtUtilTest" time="0.015"/>
  <testcase name="shouldValidateToken()" classname="microservices.authservice.authservice.utils.JwtUtilTest" time="0.021"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
