<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="microservices.authservice.authservice.utils.JwtUtilTest" tests="4" skipped="0" failures="0" errors="0" timestamp="2025-05-29T09:35:06" hostname="LHN-820-180225" time="0.218">
  <properties/>
  <testcase name="shouldGenerateValidToken()" classname="microservices.authservice.authservice.utils.JwtUtilTest" time="0.194"/>
  <testcase name="shouldGenerateRefreshToken()" classname="microservices.authservice.authservice.utils.JwtUtilTest" time="0.005"/>
  <testcase name="shouldDistinguishRefreshToken()" classname="microservices.authservice.authservice.utils.JwtUtilTest" time="0.006"/>
  <testcase name="shouldValidateToken()" classname="microservices.authservice.authservice.utils.JwtUtilTest" time="0.01"/>
  <system-out><![CDATA[16:35:06.708 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'secret' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@42b21d99] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [myTestSecretKeyThatIsLongEnoughForHS256Algorithm]
16:35:06.709 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'expiration' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@42b21d99] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [86400000]
16:35:06.709 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'refreshExpiration' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@42b21d99] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [604800000]
16:35:06.895 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'secret' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@21c815e4] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [myTestSecretKeyThatIsLongEnoughForHS256Algorithm]
16:35:06.895 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'expiration' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@21c815e4] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [86400000]
16:35:06.895 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'refreshExpiration' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@21c815e4] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [604800000]
16:35:06.901 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'secret' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@4f66ffc8] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [myTestSecretKeyThatIsLongEnoughForHS256Algorithm]
16:35:06.901 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'expiration' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@4f66ffc8] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [86400000]
16:35:06.901 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'refreshExpiration' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@4f66ffc8] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [604800000]
16:35:06.908 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'secret' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@44bd4b0a] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [myTestSecretKeyThatIsLongEnoughForHS256Algorithm]
16:35:06.908 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'expiration' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@44bd4b0a] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [86400000]
16:35:06.909 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'refreshExpiration' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@44bd4b0a] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [604800000]
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
