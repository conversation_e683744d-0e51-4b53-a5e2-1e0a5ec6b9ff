<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="microservices.authservice.authservice.utils.JwtUtilTest" tests="4" skipped="0" failures="0" errors="0" timestamp="2025-05-28T08:57:41" hostname="LHN-820-180225" time="0.415">
  <properties/>
  <testcase name="shouldGenerateValidToken()" classname="microservices.authservice.authservice.utils.JwtUtilTest" time="0.379"/>
  <testcase name="shouldGenerateRefreshToken()" classname="microservices.authservice.authservice.utils.JwtUtilTest" time="0.009"/>
  <testcase name="shouldDistinguishRefreshToken()" classname="microservices.authservice.authservice.utils.JwtUtilTest" time="0.01"/>
  <testcase name="shouldValidateToken()" classname="microservices.authservice.authservice.utils.JwtUtilTest" time="0.012"/>
  <system-out><![CDATA[15:57:41.464 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'secret' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@31e72cbc] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [myTestSecretKeyThatIsLongEnoughForHS256Algorithm]
15:57:41.466 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'expiration' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@31e72cbc] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [86400000]
15:57:41.466 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'refreshExpiration' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@31e72cbc] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [604800000]
15:57:41.827 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'secret' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@49ede9c7] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [myTestSecretKeyThatIsLongEnoughForHS256Algorithm]
15:57:41.827 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'expiration' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@49ede9c7] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [86400000]
15:57:41.828 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'refreshExpiration' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@49ede9c7] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [604800000]
15:57:41.837 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'secret' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@4207609e] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [myTestSecretKeyThatIsLongEnoughForHS256Algorithm]
15:57:41.838 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'expiration' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@4207609e] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [86400000]
15:57:41.838 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'refreshExpiration' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@4207609e] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [604800000]
15:57:41.849 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'secret' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@360bc645] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [myTestSecretKeyThatIsLongEnoughForHS256Algorithm]
15:57:41.850 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'expiration' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@360bc645] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [86400000]
15:57:41.850 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'refreshExpiration' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@360bc645] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [604800000]
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
