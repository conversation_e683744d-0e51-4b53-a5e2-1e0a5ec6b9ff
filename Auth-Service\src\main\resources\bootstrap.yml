spring:
  application:
    name: auth-service
  
  cloud:
    config:
      enabled: true
      uri: ${CONFIG_SERVER_URL:http://localhost:9296}
      fail-fast: false
      retry:
        initial-interval: 1000
        max-attempts: 6
        max-interval: 2000
        multiplier: 1.1
      request-connect-timeout: 5000
      request-read-timeout: 5000
    discovery:
      enabled: false

# Management for bootstrap
management:
  endpoints:
    web:
      exposure:
        include: health,info
