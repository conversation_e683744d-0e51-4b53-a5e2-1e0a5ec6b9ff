# IntelliJ IDEA Run Configurations

## C<PERSON><PERSON> chạy Auth Service trong IntelliJ IDEA

### 1. Import Project
1. Mở IntelliJ IDEA
2. File > Open > Ch<PERSON><PERSON> thư mục `Auth-Service`
3. <PERSON><PERSON><PERSON> "Import Gradle project"
4. <PERSON><PERSON><PERSON>radle sync hoàn thành

### 2. <PERSON><PERSON>u hình Environment Variables
Trong IntelliJ IDEA:
1. Run > Edit Configurations
2. Tạo new Application configuration
3. Main class: `microservices.authservice.authservice.AuthServiceApplication`
4. Environment variables:
   ```
   GITHUB_CLIENT_ID=Ov23l1PpstZ09LdPgcvV
   GITHUB_CLIENT_SECRET=your-github-client-secret-here
   JWT_SECRET=myVerySecretKeyForJWTTokenGenerationThatShouldBeAtLeast256BitsLong
   FRONTEND_URL=http://localhost:3000
   ```

### 3. <PERSON><PERSON><PERSON> <PERSON>ình Database
Đảm bảo MySQL đang chạy và có database `auth_service_db`:

```sql
CREATE DATABASE auth_service_db;
```

### 4. <PERSON><PERSON>y với Profile
Để chạy với development profile:
1. VM options: `-Dspring.profiles.active=dev`
2. Hoặc Program arguments: `--spring.profiles.active=dev`

### 5. GitHub OAuth Setup
Trong GitHub OAuth App settings, thêm Authorization callback URL:
```
http://localhost:8084/oauth2/callback/github
```

### 6. Test Endpoints
Sau khi service chạy thành công:

- Health check: `GET http://localhost:8084/api/auth/health`
- OAuth login: `GET http://localhost:8084/oauth2/authorize/github`
- Eureka: Service sẽ register với Eureka tại `http://localhost:8761`

### 7. Troubleshooting
- Nếu gặp lỗi JAVA_HOME: Đảm bảo Project SDK được set đúng (File > Project Structure > Project > Project SDK)
- Nếu gặp lỗi database: Kiểm tra MySQL connection và database đã tạo
- Nếu gặp lỗi OAuth: Kiểm tra GitHub Client ID/Secret và callback URL
