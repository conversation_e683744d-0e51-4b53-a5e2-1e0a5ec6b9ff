package microservices.productservice.productservice;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.test.context.ActiveProfiles;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest
@ActiveProfiles("test")
@Slf4j
class MongoConnectionTest {

    @Autowired(required = false)
    private MongoTemplate mongoTemplate;

    @Test
    void testMongoConnectionOptional() {
        if (mongoTemplate != null) {
            log.info("MongoDB is available - testing connection");
            
            // Test basic MongoDB operations
            String databaseName = mongoTemplate.getDb().getName();
            log.info("Connected to MongoDB database: {}", databaseName);
            
            assertThat(databaseName).isNotNull();
            
            log.info("MongoDB connection test passed");
        } else {
            log.warn("MongoDB is not available - skipping connection test");
        }
    }
}
