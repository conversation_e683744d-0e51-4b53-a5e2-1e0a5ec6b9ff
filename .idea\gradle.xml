<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="GradleMigrationSettings" migrationVersion="1" />
  <component name="GradleSettings">
    <option name="linkedExternalProjectsSettings">
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Auth-Service" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$/Auth-Service" />
          </set>
        </option>
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Config-Server" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$/Config-Server" />
          </set>
        </option>
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Gateway-Service" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$/Gateway-Service" />
          </set>
        </option>
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Order-Service" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$/Order-Service" />
          </set>
        </option>
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Payment-Service" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$/Payment-Service" />
          </set>
        </option>
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Product-Service" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$/Product-Service" />
          </set>
        </option>
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Service-Registry" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$/Service-Registry" />
          </set>
        </option>
      </GradleProjectSettings>
    </option>
  </component>
</project>