package microservices.productservice.productservice.config;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.context.annotation.Import;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@ActiveProfiles("test")
@Import(TestConfig.class)
class RedisConnectionTest {

    @Test
    void contextLoadsWithMockedRedis() {
        // This test verifies that the application context loads successfully
        // with mocked Redis dependencies
    }
}
