package microservices.productservice.productservice.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.config.AbstractMongoClientConfiguration;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

@Configuration
@EnableMongoRepositories(basePackages = "microservices.productservice.productservice.repository.read")
@ConditionalOnProperty(name = "spring.data.mongodb.uri")
@Slf4j
public class MongoConfig extends AbstractMongoClientConfiguration {

    @Override
    protected String getDatabaseName() {
        return "product_db";
    }

    @Override
    protected boolean autoIndexCreation() {
        return true; // Automatically create indexes
    }
}
