spring:
  application:
    name: PRODUCT-SERVICE

  # Disable Config Server
  cloud:
    config:
      enabled: false
      fail-fast: false
    compatibility-verifier:
      enabled: false
    discovery:
      enabled: true

  # Database Configuration
  datasource:
    url: *************************************************************************************
    username: root
    password: ${DB_PASSWORD:mk123654}
    driver-class-name: com.mysql.cj.jdbc.Driver

  jpa:
    open-in-view: false
    show-sql: true
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        validator:
          apply_to_ddl: false
        dialect: org.hibernate.dialect.MySQLDialect

# Server Configuration
server:
  port: 8089

# Eureka Service Discovery
eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_URL:http://localhost:8761/eureka/}
    fetch-registry: true
    register-with-eureka: true
  instance:
    hostname: localhost
    prefer-ip-address: true
    instance-id: ${spring.application.name}:${server.port}

# Management & Monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
  tracing:
    enabled: false

# Logging Configuration
logging:
  level:
    microservices.productservice: INFO
    org.springframework.cloud: ERROR
    io.micrometer: ERROR

