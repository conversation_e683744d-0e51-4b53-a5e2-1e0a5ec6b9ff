spring:
  cloud:
    compatibility-verifier:
      enabled: false
  thymeleaf:
    cache: false
  datasource:
    url: ********************************************************************************
    username: root
    password: mk123654
  jpa:
    open-in-view: false
    show-sql: true
    hibernate:
      ddl-auto: update
      properties:
        hibernate:
          validator:
            apply_to_ddl: false
        dialect: org.hibernate.dialect.MySQL5Dialect
  application:
    name: PAYMENT-SERVICE
  config:
    import: configserver:http://localhost:9296
server:
  port: 8084
management:
  tracing:
    sampling:
      probability: 1.0