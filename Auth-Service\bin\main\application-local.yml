server:
  port: 8084

spring:
  application:
    name: auth-service
  
  config:
    import: "optional:configserver:"
  
  cloud:
    config:
      enabled: false
      fail-fast: false
    discovery:
      enabled: false
  
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  h2:
    console:
      enabled: true
      path: /h2-console
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
  
  security:
    oauth2:
      client:
        registration:
          github:
            client-id: ${GITHUB_CLIENT_ID:Ov23l1PpstZ09LdPgcvV}
            client-secret: ${GITHUB_CLIENT_SECRET:your-github-client-secret}
            scope:
              - user:email
              - read:user
            redirect-uri: "{baseUrl}/oauth2/callback/{registrationId}"
        provider:
          github:
            authorization-uri: https://github.com/login/oauth/authorize
            token-uri: https://github.com/login/oauth/access_token
            user-info-uri: https://api.github.com/user
            user-name-attribute: id

eureka:
  client:
    enabled: false
    register-with-eureka: false
    fetch-registry: false

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
  tracing:
    enabled: false
  zipkin:
    tracing:
      endpoint: ""

jwt:
  secret: ${JWT_SECRET:myVerySecretKeyForJWTTokenGenerationThatShouldBeAtLeast256BitsLongForLocal}
  expiration: 86400000 # 24 hours in milliseconds
  refresh:
    expiration: 604800000 # 7 days in milliseconds

app:
  oauth2:
    authorized-redirect-uri: ${FRONTEND_URL:http://localhost:3000}/oauth2/redirect

logging:
  level:
    microservices.authservice: DEBUG
    org.springframework.security: INFO
    org.springframework.cloud: WARN
    org.springframework.boot.autoconfigure: WARN
