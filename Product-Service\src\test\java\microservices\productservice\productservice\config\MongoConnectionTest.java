package microservices.productservice.productservice.config;

import lombok.extern.slf4j.Slf4j;
import microservices.productservice.productservice.readmodel.ProductReadModel;
import microservices.productservice.productservice.repository.read.ProductReadRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest
@ActiveProfiles("test")
@Slf4j
class MongoConnectionTest {

    @Autowired(required = false)
    private ProductReadRepository productReadRepository;

    @Test
    void testMongoConnectionOptional() {
        if (productReadRepository != null) {
            log.info("MongoDB is available - testing connection");
            
            // Create a test product read model
            ProductReadModel testProduct = ProductReadModel.builder()
                    .productId(999L)
                    .name("Test Product")
                    .price(new BigDecimal("99.99"))
                    .quantity(10)
                    .status("ACTIVE")
                    .isAvailable(true)
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .version(1L)
                    .build();
            
            // Save and verify
            ProductReadModel saved = productReadRepository.save(testProduct);
            assertThat(saved.getId()).isNotNull();
            assertThat(saved.getProductId()).isEqualTo(999L);
            
            // Clean up
            productReadRepository.delete(saved);
            
            log.info("MongoDB connection test passed");
        } else {
            log.warn("MongoDB is not available - skipping connection test");
        }
    }
}
