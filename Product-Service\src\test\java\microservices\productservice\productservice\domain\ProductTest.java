package microservices.productservice.productservice.domain;

import microservices.productservice.productservice.domain.entity.Product;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

class ProductTest {

    @Test
    void shouldCreateProductWithValidData() {
        // Given & When
        Product product = Product.builder()
                .name("Test Product")
                .price(new BigDecimal("99.99"))
                .quantity(10)
                .description("Test description")
                .categoryId(1L)
                .brandId(1L)
                .status(Product.ProductStatus.ACTIVE)
                .build();

        // Then
        assertThat(product.getName()).isEqualTo("Test Product");
        assertThat(product.getPrice()).isEqualTo(new BigDecimal("99.99"));
        assertThat(product.getQuantity()).isEqualTo(10);
        assertThat(product.getStatus()).isEqualTo(Product.ProductStatus.ACTIVE);
        assertThat(product.isAvailable()).isTrue();
    }

    @Test
    void shouldReduceQuantitySuccessfully() {
        // Given
        Product product = Product.builder()
                .quantity(10)
                .status(Product.ProductStatus.ACTIVE)
                .build();

        // When
        product.reduceQuantity(3);

        // Then
        assertThat(product.getQuantity()).isEqualTo(7);
    }

    @Test
    void shouldThrowExceptionWhenReducingMoreThanAvailable() {
        // Given
        Product product = Product.builder()
                .quantity(5)
                .status(Product.ProductStatus.ACTIVE)
                .build();

        // When & Then
        assertThatThrownBy(() -> product.reduceQuantity(10))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Insufficient quantity");
    }

    @Test
    void shouldAddQuantitySuccessfully() {
        // Given
        Product product = Product.builder()
                .quantity(5)
                .status(Product.ProductStatus.ACTIVE)
                .build();

        // When
        product.addQuantity(3);

        // Then
        assertThat(product.getQuantity()).isEqualTo(8);
    }

    @Test
    void shouldReturnFalseForAvailabilityWhenOutOfStock() {
        // Given
        Product product = Product.builder()
                .quantity(0)
                .status(Product.ProductStatus.ACTIVE)
                .build();

        // When & Then
        assertThat(product.isAvailable()).isFalse();
    }

    @Test
    void shouldReturnFalseForAvailabilityWhenInactive() {
        // Given
        Product product = Product.builder()
                .quantity(10)
                .status(Product.ProductStatus.INACTIVE)
                .build();

        // When & Then
        assertThat(product.isAvailable()).isFalse();
    }
}
