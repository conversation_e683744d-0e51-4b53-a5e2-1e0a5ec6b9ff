<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="65f3c890-9f79-44a4-8e18-4398fc7d5ba5" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/.idea/compiler.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/git_toolbox_blame.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/modules/auth-service.main.iml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/gradle.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/gradle.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/misc.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/misc.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/modules.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/modules.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Order-Service/src/main/java/com/example/orderservice/config/SecurityConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/Order-Service/src/main/java/com/example/orderservice/config/SecurityConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Order-Service/src/main/java/com/example/orderservice/controller/OrderController.java" beforeDir="false" afterPath="$PROJECT_DIR$/Order-Service/src/main/java/com/example/orderservice/controller/OrderController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Payment-Service/src/main/java/com/example/paymentservice/config/Config.java" beforeDir="false" afterPath="$PROJECT_DIR$/Payment-Service/src/main/java/com/example/paymentservice/config/Config.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Payment-Service/src/main/java/com/example/paymentservice/config/SecurityConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/Payment-Service/src/main/java/com/example/paymentservice/config/SecurityConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Product-Service/build.gradle" beforeDir="false" afterPath="$PROJECT_DIR$/Product-Service/build.gradle" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Product-Service/src/main/java/microservices/productservice/productservice/ProductServiceApplication.java" beforeDir="false" afterPath="$PROJECT_DIR$/Product-Service/src/main/java/microservices/productservice/productservice/ProductServiceApplication.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Product-Service/src/main/java/microservices/productservice/productservice/config/SecurityConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Product-Service/src/main/java/microservices/productservice/productservice/controller/ProductController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Product-Service/src/main/java/microservices/productservice/productservice/entity/Product.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Product-Service/src/main/java/microservices/productservice/productservice/repository/ProductRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Product-Service/src/main/java/microservices/productservice/productservice/service/ProductService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Product-Service/src/main/java/microservices/productservice/productservice/service/serviceIpm/ProductServiceIpm.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Product-Service/src/main/java/microservices/productservice/productservice/utils/Constant.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Product-Service/src/main/resources/application.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/Product-Service/src/main/resources/application.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Product-Service/src/test/java/microservices/productservice/productservice/ProductServiceApplicationTests.java" beforeDir="false" afterPath="$PROJECT_DIR$/Product-Service/src/test/java/microservices/productservice/productservice/ProductServiceApplicationTests.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$/Auth-Service">
      <ProjectState />
    </projectState>
    <projectState path="$PROJECT_DIR$/Config-Server">
      <ProjectState />
    </projectState>
    <projectState path="$PROJECT_DIR$/Gateway-Service">
      <ProjectState />
    </projectState>
    <projectState path="$PROJECT_DIR$/Order-Service">
      <ProjectState />
    </projectState>
    <projectState path="$PROJECT_DIR$/Payment-Service">
      <ProjectState />
    </projectState>
    <projectState path="$PROJECT_DIR$/Product-Service">
      <ProjectState />
    </projectState>
    <projectState path="$PROJECT_DIR$/Service-Registry">
      <ProjectState />
    </projectState>
  </component>
  <component name="ExternalProjectsManager">
    <system id="GRADLE">
      <state>
        <task path="$PROJECT_DIR$/Service-Registry">
          <activation />
        </task>
        <task path="$PROJECT_DIR$/Auth-Service">
          <activation />
        </task>
        <task path="$PROJECT_DIR$/Gateway-Service">
          <activation />
        </task>
        <task path="$PROJECT_DIR$/Product-Service">
          <activation />
        </task>
        <projects_view>
          <tree_state>
            <expand>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="Product-Service" type="f1a62948:ProjectNode" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="Product-Service" type="f1a62948:ProjectNode" />
                <item name="Tasks" type="e4a08cd1:TasksNode" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="Product-Service" type="f1a62948:ProjectNode" />
                <item name="Tasks" type="e4a08cd1:TasksNode" />
                <item name="application" type="c8890929:TasksNode$1" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="Product-Service" type="f1a62948:ProjectNode" />
                <item name="Tasks" type="e4a08cd1:TasksNode" />
                <item name="build" type="c8890929:TasksNode$1" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="Product-Service" type="f1a62948:ProjectNode" />
                <item name="Dependencies" type="6de06a37:ExternalSystemViewDefaultContributor$MyDependenciesNode" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="Product-Service" type="f1a62948:ProjectNode" />
                <item name="Dependencies" type="6de06a37:ExternalSystemViewDefaultContributor$MyDependenciesNode" />
                <item name="testCompileClasspath" type="62daadca:ExternalSystemViewDefaultContributor$DependencyScopeExternalSystemNode" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="Product-Service" type="f1a62948:ProjectNode" />
                <item name="Dependencies" type="6de06a37:ExternalSystemViewDefaultContributor$MyDependenciesNode" />
                <item name="testRuntimeClasspath" type="62daadca:ExternalSystemViewDefaultContributor$DependencyScopeExternalSystemNode" />
              </path>
            </expand>
            <select />
          </tree_state>
        </projects_view>
      </state>
    </system>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2v4unesI4HwKqyDlEE9nhbh0n96" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="autoscrollToSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ASKED_ADD_EXTERNAL_FILES": "true",
    "Gradle.Auth-Service [build].executor": "Run",
    "Gradle.Auth-Service [clean].executor": "Run",
    "Gradle.Build Auth-Service.executor": "Run",
    "Gradle.Build Config-Server.executor": "Run",
    "Gradle.Build Gateway-Service.executor": "Run",
    "Gradle.Build Order-Service.executor": "Run",
    "Gradle.Build Payment-Service.executor": "Run",
    "Gradle.Build Product-Service.executor": "Run",
    "Gradle.Build Service-Registry.executor": "Run",
    "Gradle.Gateway-Service [build].executor": "Debug",
    "Gradle.Gateway-Service [clean].executor": "Run",
    "Gradle.Product-Service [build].executor": "Debug",
    "Gradle.Product-Service [clean].executor": "Run",
    "Gradle.ProductCommandHandlerTest.shouldHandleCreateProductCommandSuccessfully.executor": "Run",
    "Gradle.Service-Registry [build].executor": "Run",
    "Gradle.Service-Registry [clean].executor": "Run",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "Spring Boot.AuthServiceApplication.executor": "Debug",
    "Spring Boot.ConfigServerApplication.executor": "Debug",
    "Spring Boot.GatewayServiceApplication (1).executor": "Debug",
    "Spring Boot.GatewayServiceApplication.executor": "Debug",
    "Spring Boot.OrderServiceApplication.executor": "Debug",
    "Spring Boot.PaymentServiceApplication.executor": "Debug",
    "Spring Boot.ProductServiceApplication.executor": "Debug",
    "Spring Boot.ServiceRegistryApplication.executor": "Debug",
    "database.data.extractors.current.export.id": "SQL Inserts",
    "database.data.extractors.current.id": "SQL Inserts",
    "git-widget-placeholder": "master",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/shopping_microservices/Auth-Service",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Modules",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.2",
    "run.configurations.included.in.services": "true",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "redis",
      "mongo",
      "postgresql"
    ]
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Gradle.ProductCommandHandlerTest.shouldHandleCreateProductCommandSuccessfully">
    <configuration name="products.sql" type="DatabaseScript" editBeforeRun="true" temporary="true" nameIsGenerated="true">
      <script-file value="c01c9bc9-f63f-480a-8e4b-03ce444bda2a/database/product_db/schema/public/table/products.sql" />
      <script-mode>FILE</script-mode>
      <method v="2" />
    </configuration>
    <configuration name="Auth-Service [build]" type="GradleRunConfiguration" factoryName="Gradle" temporary="true">
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" value="$PROJECT_DIR$/Auth-Service" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list>
            <option value="build" />
          </list>
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>true</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>false</RunAsTest>
      <method v="2" />
    </configuration>
    <configuration name="Product-Service [build]" type="GradleRunConfiguration" factoryName="Gradle" temporary="true">
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" value="$PROJECT_DIR$/Product-Service" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list>
            <option value="build" />
          </list>
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>true</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>false</RunAsTest>
      <method v="2" />
    </configuration>
    <configuration name="Product-Service [clean]" type="GradleRunConfiguration" factoryName="Gradle" temporary="true">
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" value="$PROJECT_DIR$/Product-Service" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list>
            <option value="clean" />
          </list>
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>true</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>false</RunAsTest>
      <method v="2" />
    </configuration>
    <configuration name="ProductCommandHandlerTest.shouldHandleCreateProductCommandSuccessfully" type="GradleRunConfiguration" factoryName="Gradle" temporary="true">
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" value="$PROJECT_DIR$/Product-Service" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" value="" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list>
            <option value=":test" />
            <option value="--tests" />
            <option value="&quot;microservices.productservice.productservice.cqrs.handlers.ProductCommandHandlerTest.shouldHandleCreateProductCommandSuccessfully&quot;" />
          </list>
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>false</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>true</RunAsTest>
      <method v="2" />
    </configuration>
    <configuration name="AuthServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="ACTIVE_PROFILES" value="-Dspring.profiles.active=local" />
      <option name="ENABLE_LAUNCH_OPTIMIZATION" value="false" />
      <envs>
        <env name="GITHUB_CLIENT_ID" value="Ov23l1PpstZ09LdPgcvV" />
        <env name="GITHUB_CLIENT_SECRET" value="abc" />
        <env name="JWT_SECRET" value="myVerySecretKeyForJWTTokenGenerationThatShouldBeAtLeast256BitsLong" />
      </envs>
      <module name="auth-service.main" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="microservices.authservice.authservice.AuthServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ConfigServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="Config-Server.main" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.configserver.ConfigServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GatewayServiceApplication (1)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="Gateway-Service.main" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="microservices.gatewayservice.gatewayservice.GatewayServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="OrderServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="Order-Service.main" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.orderservice.OrderServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="PaymentServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="Payment-Service.main" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.paymentservice.PaymentServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ProductServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="Product-Service.main" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="microservices.productservice.productservice.ProductServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ServiceRegistryApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="Service-Registry.main" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.serviceregistry.ServiceRegistryApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Database Script.products.sql" />
      <item itemvalue="Gradle.Product-Service [build]" />
      <item itemvalue="Gradle.Product-Service [clean]" />
      <item itemvalue="Gradle.ProductCommandHandlerTest.shouldHandleCreateProductCommandSuccessfully" />
      <item itemvalue="Gradle.Auth-Service [build]" />
      <item itemvalue="Spring Boot.ServiceRegistryApplication" />
      <item itemvalue="Spring Boot.GatewayServiceApplication (1)" />
      <item itemvalue="Spring Boot.AuthServiceApplication" />
      <item itemvalue="Spring Boot.ConfigServerApplication" />
      <item itemvalue="Spring Boot.OrderServiceApplication" />
      <item itemvalue="Spring Boot.PaymentServiceApplication" />
      <item itemvalue="Spring Boot.ProductServiceApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Gradle.ProductCommandHandlerTest.shouldHandleCreateProductCommandSuccessfully" />
        <item itemvalue="Gradle.Product-Service [build]" />
        <item itemvalue="Gradle.Product-Service [clean]" />
        <item itemvalue="Database Script.products.sql" />
        <item itemvalue="Gradle.Auth-Service [build]" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-18abd8497189-intellij.indexing.shared.core-IU-241.14494.240" />
        <option value="bundled-js-predefined-1d06a55b98c1-74d2a5396914-JavaScript-IU-241.14494.240" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="65f3c890-9f79-44a4-8e18-4398fc7d5ba5" name="Changes" comment="" />
      <created>1743416836513</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1743416836513</updated>
      <workItem from="1743416837361" duration="75000" />
      <workItem from="1743473631985" duration="13973000" />
      <workItem from="1743648901512" duration="864000" />
      <workItem from="1745919509608" duration="730000" />
      <workItem from="1746506058658" duration="1100000" />
      <workItem from="1748420325861" duration="820000" />
      <workItem from="1748421186643" duration="6190000" />
      <workItem from="1748508707716" duration="2861000" />
      <workItem from="1748827852051" duration="13941000" />
      <workItem from="1748915686574" duration="5933000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>