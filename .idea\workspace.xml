<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="65f3c890-9f79-44a4-8e18-4398fc7d5ba5" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/.idea/compiler.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/git_toolbox_blame.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/gradle.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/gradle.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/misc.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/misc.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/modules.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/modules.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Product-Service/src/main/java/microservices/productservice/productservice/config/SecurityConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/Product-Service/src/main/java/microservices/productservice/productservice/config/SecurityConfig.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$/Auth-Service">
      <ProjectState />
    </projectState>
    <projectState path="$PROJECT_DIR$/Config-Server">
      <ProjectState />
    </projectState>
    <projectState path="$PROJECT_DIR$/Gateway-Service">
      <ProjectState />
    </projectState>
    <projectState path="$PROJECT_DIR$/Order-Service">
      <ProjectState />
    </projectState>
    <projectState path="$PROJECT_DIR$/Payment-Service">
      <ProjectState />
    </projectState>
    <projectState path="$PROJECT_DIR$/Product-Service">
      <ProjectState />
    </projectState>
    <projectState path="$PROJECT_DIR$/Service-Registry">
      <ProjectState />
    </projectState>
  </component>
  <component name="ExternalProjectsManager">
    <system id="GRADLE">
      <state>
        <task path="$PROJECT_DIR$/Service-Registry">
          <activation />
        </task>
        <projects_view>
          <tree_state>
            <expand>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="Config-Server" type="f1a62948:ProjectNode" />
              </path>
            </expand>
            <select />
          </tree_state>
        </projects_view>
      </state>
    </system>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2v4unesI4HwKqyDlEE9nhbh0n96" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="autoscrollToSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Gradle.Build Auth-Service.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.Build Config-Server.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.Build Order-Service.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.Build Payment-Service.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.Build Product-Service.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.Build Service-Registry.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.Service-Registry [build].executor&quot;: &quot;Run&quot;,
    &quot;Gradle.Service-Registry [clean].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;Spring Boot.AuthServiceApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.ConfigServerApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.OrderServiceApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.PaymentServiceApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.ProductServiceApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.ServiceRegistryApplication.executor&quot;: &quot;Debug&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/shopping_microservices/Auth-Service&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;run.configurations.included.in.services&quot;: &quot;true&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.AuthServiceApplication">
    <configuration name="Service-Registry [build]" type="GradleRunConfiguration" factoryName="Gradle" temporary="true">
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" value="$PROJECT_DIR$/Service-Registry" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list>
            <option value="build" />
          </list>
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>true</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>false</RunAsTest>
      <method v="2" />
    </configuration>
    <configuration name="Service-Registry [clean]" type="GradleRunConfiguration" factoryName="Gradle" temporary="true">
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" value="$PROJECT_DIR$/Service-Registry" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list>
            <option value="clean" />
          </list>
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>true</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>false</RunAsTest>
      <method v="2" />
    </configuration>
    <configuration name="AuthServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="ACTIVE_PROFILES" value="-Dspring.profiles.active=local" />
      <option name="ENABLE_LAUNCH_OPTIMIZATION" value="false" />
      <envs>
        <env name="GITHUB_CLIENT_ID" value="Ov23l1PpstZ09LdPgcvV GITHUB_CLIENT_SECRET=your-actual-secret-from-github " />
      </envs>
      <module name="auth-service.main" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="microservices.authservice.authservice.AuthServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ConfigServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="Config-Server.main" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.configserver.ConfigServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="OrderServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="Order-Service.main" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.orderservice.OrderServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="PaymentServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="Payment-Service.main" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.paymentservice.PaymentServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ProductServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="Product-Service.main" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="microservices.productservice.productservice.ProductServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ServiceRegistryApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="Service-Registry.main" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.serviceregistry.ServiceRegistryApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Gradle.Service-Registry [build]" />
      <item itemvalue="Gradle.Service-Registry [clean]" />
      <item itemvalue="Spring Boot.AuthServiceApplication" />
      <item itemvalue="Spring Boot.ConfigServerApplication" />
      <item itemvalue="Spring Boot.OrderServiceApplication" />
      <item itemvalue="Spring Boot.PaymentServiceApplication" />
      <item itemvalue="Spring Boot.ProductServiceApplication" />
      <item itemvalue="Spring Boot.ServiceRegistryApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Gradle.Service-Registry [build]" />
        <item itemvalue="Gradle.Service-Registry [clean]" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-18abd8497189-intellij.indexing.shared.core-IU-241.14494.240" />
        <option value="bundled-js-predefined-1d06a55b98c1-74d2a5396914-JavaScript-IU-241.14494.240" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="65f3c890-9f79-44a4-8e18-4398fc7d5ba5" name="Changes" comment="" />
      <created>1743416836513</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1743416836513</updated>
      <workItem from="1743416837361" duration="75000" />
      <workItem from="1743473631985" duration="13973000" />
      <workItem from="1743648901512" duration="864000" />
      <workItem from="1745919509608" duration="730000" />
      <workItem from="1746506058658" duration="1100000" />
      <workItem from="1748420325861" duration="820000" />
      <workItem from="1748421186643" duration="1327000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>