spring:
  # Development profile - minimal dependencies
  
  # Write Database - PostgreSQL
  datasource:
    url: ********************************************
    username: ${DB_USERNAME:admin}
    password: ${DB_PASSWORD:pz;H8UgB}
    driver-class-name: org.postgresql.Driver

  jpa:
    show-sql: true
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true

  # MongoDB - Optional for development
  data:
    mongodb:
      host: ${MONGO_HOST:localhost}
      port: ${MONGO_PORT:27017}
      database: ${MONGO_DATABASE:product_read_db}
      username: ${MONGO_USERNAME:admin}
      password: ${MONGO_PASSWORD:mk123654}
      authentication-database: admin

  # Redis - Optional for development
  cache:
    type: none  # Disable caching for development

  # Kafka - Optional for development
  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:localhost:29092}

# Logging for development
logging:
  level:
    microservices.productservice: DEBUG
    org.springframework.data.mongodb: INFO
    org.springframework.kafka: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
