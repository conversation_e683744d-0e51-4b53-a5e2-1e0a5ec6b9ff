server:
  port: 9090

spring:
  zipkin:
    base-url: http://*************:9411 # <PERSON><PERSON><PERSON> chỉ của Zipkin server
    sleuth:
      sampler:
        probability: 1.0 # <PERSON><PERSON><PERSON> su<PERSON>t ghi nhận truy vết
      enabled: true
    zipkin:
      enabled: true
  application:
    name: GATEWAY-SERVICE
  config:
    import: configserver:http://localhost:9296
  security:
    oauth2:
      client:
        registration:
          github: # Thay 'okta' bằng 'github'
            client-id: ********************      # Thay bằng Client ID từ GitHub
            client-secret: 3ecf95ed3c95bcd08257fd2a3ae3c690251c17fa  # Thay bằng Client Secret từ GitHub
            scope: user:email  # Scope để lấy email người dùng
            authorization-grant-type: authorization_code
            redirect-uri: http://localhost:9090/login/oauth2/code/github
        provider:
          github: # Cấu hình provider GitHub
            authorization-uri: https://github.com/login/oauth/authorize
            token-uri: https://github.com/login/oauth/access_token
            user-info-uri: https://api.github.com/user
            user-name-attribute: login
  cloud:
    gateway:
      routes:
        - id: AUTH-SERVICE
          uri: lb://auth-service
          predicates:
            - Path=/api/auth/**,/oauth2/**
          filters:
            - name: CircuitBreaker
              args:
                name: AUTH-SERVICE
                fallbackuri: forward:/authServiceFallBack
        - id: ORDER-SERVICE
          uri: lb://ORDER-SERVICE
          predicates:
            - Path=/api/order/**
          filters:
            - name: CircuitBreaker
              args:
                name: ORDER-SERVICE
                fallbackuri: forward:/orderServiceFallBack
            - name: AuthenticationFilter
            - TokenRelay=                        # Chuyển token đến ORDER-SERVICE
        - id: PAYMENT-SERVICE
          uri: lb://PAYMENT-SERVICE
          predicates:
            - Path=/api/payment/**
          filters:
            - name: CircuitBreaker
              args:
                name: PAYMENT-SERVICE
                fallbackuri: forward:/paymentServiceFallBack
            - name: AuthenticationFilter
            - TokenRelay=                        # Chuyển token đến PAYMENT-SERVICE
        - id: PRODUCT-SERVICE
          uri: lb://PRODUCT-SERVICE
          predicates:
            - Path=/api/product/**
          filters:
            - name: CircuitBreaker
              args:
                name: PRODUCT-SERVICE
                fallbackuri: forward:/productServiceFallBack
            - name: AuthenticationFilter
            - TokenRelay=                        # Chuyển token đến PRODUCT-SERVICE

# Auth Service Configuration
auth:
  service:
    url: http://localhost:8084

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
