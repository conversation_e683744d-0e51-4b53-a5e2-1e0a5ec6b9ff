server:
  port: 9090

spring:
  application:
    name: gateway-service

  # Disable Config Server and Tracing
  cloud:
    config:
      enabled: false
      fail-fast: false
    gateway:
      routes:
        # Auth Service - Public endpoints (no authentication required)
        - id: AUTH-SERVICE
          uri: lb://auth-service
          predicates:
            - Path=/api/auth/**,/oauth2/**
          filters:
            - name: CircuitBreaker
              args:
                name: AUTH-SERVICE
                fallbackuri: forward:/authServiceFallBack

        # Protected Services - Require authentication
        - id: ORDER-SERVICE
          uri: lb://ORDER-SERVICE
          predicates:
            - Path=/api/order/**
          filters:
            - name: CircuitBreaker
              args:
                name: ORDER-SERVICE
                fallbackuri: forward:/orderServiceFallBack
            - name: AuthenticationFilter

        - id: PAYMENT-SERVICE
          uri: lb://PAYMENT-SERVICE
          predicates:
            - Path=/api/payment/**
          filters:
            - name: CircuitBreaker
              args:
                name: PAYMENT-SERVICE
                fallbackuri: forward:/paymentServiceFallBack
            - name: AuthenticationFilter

        - id: PRODUCT-SERVICE
          uri: lb://PRODUCT-SERVICE
          predicates:
            - Path=/api/product/**
          filters:
            - name: CircuitBreaker
              args:
                name: PRODUCT-SERVICE
                fallbackuri: forward:/productServiceFallBack
            - name: AuthenticationFilter

# Auth Service Configuration
auth:
  service:
    url: http://localhost:8084

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    fetch-registry: true
    register-with-eureka: true

# Management & Monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,gateway
  endpoint:
    health:
      show-details: always
  tracing:
    enabled: false

# Logging
logging:
  level:
    org.springframework.cloud.gateway: INFO
    microservices.gatewayservice: DEBUG
    org.springframework.security: WARN
