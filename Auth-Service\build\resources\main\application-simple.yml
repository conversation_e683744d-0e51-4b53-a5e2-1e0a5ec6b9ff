server:
  port: 8084

spring:
  application:
    name: auth-service
  
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  h2:
    console:
      enabled: true
      path: /h2-console
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
  
  # Redis disabled for simple profile - using in-memory storage
  # data:
  #   redis:
  #     host: localhost
  #     port: 6379

  security:
    oauth2:
      client:
        registration:
          github:
            client-id: ${GITHUB_CLIENT_ID:Ov23l1PpstZ09LdPgcvV}
            client-secret: ${GITHUB_CLIENT_SECRET:your-github-client-secret}
            scope:
              - user:email
              - read:user
            redirect-uri: "{baseUrl}/oauth2/callback/{registrationId}"
        provider:
          github:
            authorization-uri: https://github.com/login/oauth/authorize
            token-uri: https://github.com/login/oauth/access_token
            user-info-uri: https://api.github.com/user
            user-name-attribute: id

# Disable all cloud features
eureka:
  client:
    enabled: false
    register-with-eureka: false
    fetch-registry: false

# Disable tracing completely
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always
  tracing:
    enabled: false
  zipkin:
    tracing:
      endpoint: ""
  observations:
    web:
      requests:
        enabled: false

jwt:
  secret: ${JWT_SECRET:myVerySecretKeyForJWTTokenGenerationThatShouldBeAtLeast256BitsLongForSimple}
  expiration: 86400000 # 24 hours in milliseconds
  refresh:
    expiration: 604800000 # 7 days in milliseconds

app:
  oauth2:
    authorized-redirect-uri: ${FRONTEND_URL:http://localhost:3000}/oauth2/redirect

logging:
  level:
    microservices.authservice: INFO
    org.springframework.security: WARN
    org.springframework.cloud: ERROR
    org.springframework.boot.autoconfigure: WARN
    io.micrometer: ERROR
    io.zipkin: ERROR
