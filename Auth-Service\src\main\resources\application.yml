server:
  port: 8084

spring:
  application:
    name: auth-service

  # Config Server - optional
  config:
    import: "optional:configserver:"

  cloud:
    config:
      enabled: false
      fail-fast: false
    discovery:
      enabled: false

  # Database Configuration
  datasource:
    url: *******************************************************************************************************************
    username: root
    password: ${DB_PASSWORD:mk123654}
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: true

  # Redis Configuration for Refresh Tokens
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
  
  # OAuth2 GitHub Configuration
  security:
    oauth2:
      client:
        registration:
          github:
            client-id: ${GITHUB_CLIENT_ID:Ov23l1PpstZ09LdPgcvV}
            client-secret: ${GITHUB_CLIENT_SECRET:your-github-client-secret}
            scope:
              - user:email
              - read:user
            redirect-uri: "{baseUrl}/oauth2/callback/{registrationId}"
        provider:
          github:
            authorization-uri: https://github.com/login/oauth/authorize
            token-uri: https://github.com/login/oauth/access_token
            user-info-uri: https://api.github.com/user
            user-name-attribute: id

# Eureka Service Discovery
eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_URL:http://localhost:8761/eureka/}
    fetch-registry: true
    register-with-eureka: true
  instance:
    hostname: localhost
    prefer-ip-address: true

# Management & Monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
  # Disable tracing to avoid errors
  tracing:
    enabled: false
  zipkin:
    tracing:
      endpoint: ""
  observations:
    web:
      requests:
        enabled: false

# JWT Configuration
jwt:
  secret: ${JWT_SECRET:myVerySecretKeyForJWTTokenGenerationThatShouldBeAtLeast256BitsLong}
  expiration: 86400000 # 24 hours in milliseconds
  refresh:
    expiration: 604800000 # 7 days in milliseconds

# OAuth2 Redirect Configuration
app:
  oauth2:
    authorized-redirect-uri: ${FRONTEND_URL:http://localhost:3000}/oauth2/redirect

# Logging Configuration
logging:
  level:
    microservices.authservice: INFO
    org.springframework.security: WARN
    org.springframework.cloud: ERROR
    org.springframework.data.redis: INFO
    io.micrometer: ERROR
    io.zipkin: ERROR
