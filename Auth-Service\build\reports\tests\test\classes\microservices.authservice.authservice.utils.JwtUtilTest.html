<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - JwtUtilTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>JwtUtilTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/microservices.authservice.authservice.utils.html">microservices.authservice.authservice.utils</a> &gt; JwtUtilTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">4</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.410s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Tests</a>
</li>
<li>
<a href="#tab1">Standard output</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">shouldDistinguishRefreshToken()</td>
<td class="success">0.010s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">shouldGenerateRefreshToken()</td>
<td class="success">0.009s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">shouldGenerateValidToken()</td>
<td class="success">0.379s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">shouldValidateToken()</td>
<td class="success">0.012s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div id="tab1" class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>15:57:41.464 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'secret' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@31e72cbc] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [myTestSecretKeyThatIsLongEnoughForHS256Algorithm]
15:57:41.466 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'expiration' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@31e72cbc] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [86400000]
15:57:41.466 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'refreshExpiration' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@31e72cbc] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [604800000]
15:57:41.827 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'secret' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@49ede9c7] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [myTestSecretKeyThatIsLongEnoughForHS256Algorithm]
15:57:41.827 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'expiration' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@49ede9c7] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [86400000]
15:57:41.828 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'refreshExpiration' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@49ede9c7] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [604800000]
15:57:41.837 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'secret' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@4207609e] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [myTestSecretKeyThatIsLongEnoughForHS256Algorithm]
15:57:41.838 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'expiration' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@4207609e] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [86400000]
15:57:41.838 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'refreshExpiration' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@4207609e] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [604800000]
15:57:41.849 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'secret' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@360bc645] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [myTestSecretKeyThatIsLongEnoughForHS256Algorithm]
15:57:41.850 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'expiration' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@360bc645] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [86400000]
15:57:41.850 [Test worker] DEBUG org.springframework.test.util.ReflectionTestUtils - Setting field 'refreshExpiration' of type [null] on target object [microservices.authservice.authservice.utils.JwtUtil@360bc645] or target class [class microservices.authservice.authservice.utils.JwtUtil] to value [604800000]
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.5</a> at May 28, 2025, 3:57:42 PM</p>
</div>
</div>
</body>
</html>
