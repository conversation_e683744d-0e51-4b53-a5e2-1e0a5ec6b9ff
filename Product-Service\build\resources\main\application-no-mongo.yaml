spring:
  # Profile without MongoDB - only PostgreSQL + Redis
  
  # Write Database - PostgreSQL only
  datasource:
    url: ********************************************
    username: ${DB_USERNAME:admin}
    password: ${DB_PASSWORD:pz;H8UgB}
    driver-class-name: org.postgresql.Driver

  jpa:
    show-sql: true
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true

  # Disable MongoDB auto-configuration
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration
      - org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration

  # Redis for caching only
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      timeout: 2000ms

  # Kafka for events
  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:localhost:29092}

# Logging
logging:
  level:
    microservices.productservice: DEBUG
    org.springframework.data.mongodb: OFF
    com.mongodb: OFF
