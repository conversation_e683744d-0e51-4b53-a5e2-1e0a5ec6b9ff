package microservices.productservice.productservice.integration;

import microservices.productservice.productservice.cqrs.commands.CreateProductCommand;
import microservices.productservice.productservice.cqrs.handlers.ProductCommandHandler;
import microservices.productservice.productservice.domain.entity.Product;
import microservices.productservice.productservice.repository.write.ProductWriteRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.transaction.annotation.Transactional;
import org.testcontainers.containers.KafkaContainer;
import org.testcontainers.containers.MongoDBContainer;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest
@Testcontainers
@ActiveProfiles("test")
class ProductIntegrationTest {

    @Container
    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>(DockerImageName.parse("postgres:16"))
            .withDatabaseName("test_product_db")
            .withUsername("test")
            .withPassword("test");

    @Container
    static MongoDBContainer mongodb = new MongoDBContainer(DockerImageName.parse("mongo:7.0"))
            .withExposedPorts(27017);

    @Container
    static KafkaContainer kafka = new KafkaContainer(DockerImageName.parse("confluentinc/cp-kafka:7.6.1"));

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        // PostgreSQL configuration
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
        
        // MongoDB configuration
        registry.add("spring.data.mongodb.host", mongodb::getHost);
        registry.add("spring.data.mongodb.port", mongodb::getFirstMappedPort);
        registry.add("spring.data.mongodb.database", () -> "test_product_read_db");
        
        // Kafka configuration
        registry.add("spring.kafka.bootstrap-servers", kafka::getBootstrapServers);
    }

    @Autowired
    private ProductCommandHandler productCommandHandler;

    @Autowired
    private ProductWriteRepository productWriteRepository;

    @Test
    @Transactional
    void shouldCreateProductSuccessfully() {
        // Given
        Map<String, Object> attributeValues = new HashMap<>();
        attributeValues.put("color", "Red");
        attributeValues.put("size", "Large");
        attributeValues.put("weight", 1.5);

        CreateProductCommand command = CreateProductCommand.builder()
                .name("Test Product")
                .price(new BigDecimal("99.99"))
                .quantity(10)
                .description("Test product description")
                .categoryId(1L)
                .brandId(1L)
                .attributeSetId(1L)
                .attributeValues(attributeValues)
                .status("ACTIVE")
                .build();

        // When
        Long productId = productCommandHandler.handle(command);

        // Then
        assertThat(productId).isNotNull();

        Optional<Product> savedProduct = productWriteRepository.findById(productId);
        assertThat(savedProduct).isPresent();
        assertThat(savedProduct.get().getName()).isEqualTo("Test Product");
        assertThat(savedProduct.get().getPrice()).isEqualTo(new BigDecimal("99.99"));
        assertThat(savedProduct.get().getQuantity()).isEqualTo(10);
        assertThat(savedProduct.get().getStatus()).isEqualTo(Product.ProductStatus.ACTIVE);
    }

    @Test
    void shouldFindProductById() {
        // Given
        Product product = Product.builder()
                .name("Test Product 2")
                .price(new BigDecimal("149.99"))
                .quantity(5)
                .description("Another test product")
                .categoryId(2L)
                .brandId(2L)
                .status(Product.ProductStatus.ACTIVE)
                .build();

        Product savedProduct = productWriteRepository.save(product);

        // When
        Optional<Product> foundProduct = productWriteRepository.findById(savedProduct.getId());

        // Then
        assertThat(foundProduct).isPresent();
        assertThat(foundProduct.get().getName()).isEqualTo("Test Product 2");
        assertThat(foundProduct.get().getPrice()).isEqualTo(new BigDecimal("149.99"));
    }
}
