# Local profile - disable Config Server
spring:
  cloud:
    config:
      enabled: false
      fail-fast: false
    discovery:
      enabled: false

# Disable Eureka for local development
eureka:
  client:
    enabled: false
    register-with-eureka: false
    fetch-registry: false

# Use H2 for local development
spring:
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  h2:
    console:
      enabled: true
      path: /h2-console
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect

# Disable Redis for local (use in-memory)
spring:
  data:
    redis:
      host: ""  # This will trigger fallback to in-memory

logging:
  level:
    microservices.authservice: DEBUG
    org.springframework.cloud: ERROR
